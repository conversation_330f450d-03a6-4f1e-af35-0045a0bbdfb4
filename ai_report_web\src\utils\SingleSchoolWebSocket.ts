/**
 * 单个学校分析WebSocket封装类
 * 用于处理添加院校时的流式数据接收和打字机效果显示
 */

export interface SchoolAnalysisData {
  score_formula: string;
  competition_analysis: string;
  study_suggestions: string;
}

export interface WebSocketMessage {
  type: 'start' | 'stream_data' | 'complete' | 'error';
  content?: string;
  message?: string;
  module_type?: string;
}

export interface SchoolAnalysisParams {
  report_id: string | number;
  school_name: string;
  college_name: string;
  module_type: 'score_formula' | 'competition_analysis' | 'study_suggestions';
}

export class SingleSchoolWebSocket {
  private ws: WebSocket | null = null;
  private url: string;
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 3;
  private reconnectDelay: number = 1000;

  // 回调函数
  private onConnectCallback?: () => void;
  private onMessageCallback?: (message: WebSocketMessage) => void;
  private onErrorCallback?: (error: string) => void;
  private onCloseCallback?: () => void;



  constructor(url?: string) {
    // 使用Vite代理路径，在开发环境下会自动代理到后端WebSocket服务
    this.url = url || (location.protocol === 'https:' ? 'wss:' : 'ws:') + '//' + location.host + '/ws';
  }

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket连接成功');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.onConnectCallback?.();
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.onMessageCallback?.(message);
          } catch (error) {
            console.error('解析WebSocket消息失败:', error);
            this.onErrorCallback?.('消息解析失败');
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket错误:', error);
          this.onErrorCallback?.('连接错误');
          reject(error);
        };

        this.ws.onclose = () => {
          console.log('WebSocket连接关闭');
          this.isConnected = false;
          this.onCloseCallback?.();
          this.handleReconnect();
        };

      } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        reject(error);
      }
    });
  }



  /**
   * 发送学校分析请求
   */
  sendSchoolAnalysisRequest(params: SchoolAnalysisParams): boolean {
    if (!this.isConnected || !this.ws) {
      console.error('WebSocket未连接');
      return false;
    }

    try {
      const message = {
        report_id: params.report_id,
        school_name: params.school_name,
        college_name: params.college_name,
        module_type: params.module_type
      };

      this.ws.send(JSON.stringify(message));
      console.log('发送学校分析请求:', message);

      return true;
    } catch (error) {
      console.error('发送消息失败:', error);
      this.onErrorCallback?.('发送消息失败');
      return false;
    }
  }

  /**
   * 处理重连逻辑
   */
  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('重连失败:', error);
        });
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('达到最大重连次数，停止重连');
      this.onErrorCallback?.('连接失败，请刷新页面重试');
    }
  }

  /**
   * 关闭连接
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
  }

  /**
   * 设置回调函数
   */
  onConnect(callback: () => void) {
    this.onConnectCallback = callback;
  }

  onMessage(callback: (message: WebSocketMessage) => void) {
    this.onMessageCallback = callback;
  }

  onError(callback: (error: string) => void) {
    this.onErrorCallback = callback;
  }

  onClose(callback: () => void) {
    this.onCloseCallback = callback;
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }
}

/**
 * 打字机效果工具类
 */
export class TypewriterEffect {
  private text: string = '';
  private currentIndex: number = 0;
  private speed: number = 50; // 打字速度（毫秒）
  private timer: number | null = null;
  private onUpdateCallback?: (text: string) => void;
  private onCompleteCallback?: () => void;

  constructor(speed: number = 50) {
    this.speed = speed;
  }

  /**
   * 开始打字机效果
   */
  start(text: string, onUpdate: (text: string) => void, onComplete?: () => void) {
    this.text = text;
    this.currentIndex = 0;
    this.onUpdateCallback = onUpdate;
    this.onCompleteCallback = onComplete;
    
    this.stop(); // 停止之前的动画
    this.animate();
  }

  /**
   * 追加文本并继续动画
   */
  append(additionalText: string) {
    this.text += additionalText;
    if (!this.timer) {
      this.animate();
    }
  }

  /**
   * 执行动画
   */
  private animate() {
    if (this.currentIndex < this.text.length) {
      this.currentIndex++;
      const currentText = this.text.substring(0, this.currentIndex);
      this.onUpdateCallback?.(currentText);
      
      this.timer = window.setTimeout(() => {
        this.animate();
      }, this.speed);
    } else {
      this.timer = null;
      this.onCompleteCallback?.();
    }
  }

  /**
   * 停止动画
   */
  stop() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  /**
   * 立即显示全部文本
   */
  complete() {
    this.stop();
    this.currentIndex = this.text.length;
    this.onUpdateCallback?.(this.text);
    this.onCompleteCallback?.();
  }

  /**
   * 检查是否正在动画
   */
  isAnimating(): boolean {
    return this.timer !== null;
  }
}
