<?php

namespace app\process;

use app\controller\SingleSchoolWebSocketController;
use Workerman\Worker;
use Workerman\Connection\TcpConnection;
use support\Log;

class SingleSchoolWebSocket
{
    public function onWorkerStart(Worker $worker)
    {
        Log::info('单个学校WebSocket服务器启动', [
            'listen' => $worker->getSocketName(),
            'pid' => $worker->id
        ]);
    }

    public function onConnect(TcpConnection $connection)
    {
        $controller = new SingleSchoolWebSocketController();
        $controller->onConnect($connection);
    }

    public function onMessage(TcpConnection $connection, $data)
    {
        $controller = new SingleSchoolWebSocketController();
        $controller->onMessage($connection, $data);
    }

    public function onClose(TcpConnection $connection)
    {
        $controller = new SingleSchoolWebSocketController();
        $controller->onClose($connection);
    }

    public function onError(TcpConnection $connection, $code, $msg)
    {
        Log::error('WebSocket连接错误', [
            'connection_id' => $connection->id,
            'code' => $code,
            'message' => $msg
        ]);
    }
}
