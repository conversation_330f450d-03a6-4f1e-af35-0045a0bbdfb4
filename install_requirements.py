#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装 WebSocket 测试所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package_name):
    """安装指定的包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {package_name} 安装成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败:")
        print(f"   错误信息: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 安装 {package_name} 时出现异常: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 WebSocket 测试依赖安装工具")
    print("=" * 60)
    print("")
    
    # 需要安装的包列表
    packages = [
        ("websockets", "websockets"),  # (import_name, package_name)
        ("websocket", "websocket-client"),
    ]
    
    installed_count = 0
    total_count = len(packages)
    
    for import_name, package_name in packages:
        print(f"🔍 检查 {package_name}...")
        
        if check_package(import_name):
            print(f"✅ {package_name} 已安装")
            installed_count += 1
        else:
            print(f"⚠️  {package_name} 未安装，开始安装...")
            if install_package(package_name):
                installed_count += 1
            else:
                print(f"❌ {package_name} 安装失败，请手动安装:")
                print(f"   pip install {package_name}")
        
        print("")
    
    print("=" * 60)
    print(f"📊 安装结果: {installed_count}/{total_count} 个包安装成功")
    
    if installed_count == total_count:
        print("🎉 所有依赖包安装完成!")
        print("")
        print("现在您可以运行测试脚本:")
        print("  python test_websocket.py        # 异步版本")
        print("  python test_websocket_simple.py # 同步版本")
    else:
        print("⚠️  部分依赖包安装失败，请检查网络连接或手动安装")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
