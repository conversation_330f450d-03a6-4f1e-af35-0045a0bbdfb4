# WebSocket 服务器测试工具

## 功能说明

这个测试工具用于测试单个学校分析的 WebSocket 服务器功能。

### 服务器信息
- **地址**: `ws://127.0.0.1:8791`
- **功能**: 接收学校信息，调用千问AI进行分析，返回流式数据

### 测试数据格式
```json
{
  "report_id": "1",
  "school_name": "安徽大学", 
  "college_name": "计算机科学与技术学院"
}
```

### 预期返回格式
服务器会返回包含以下模块的数据：
- **MODULE1**: 总成绩计算公式
- **MODULE2**: 竞争难度分析  
- **MODULE3**: 备考目标建议

## 使用步骤

### 1. 安装依赖
```bash
# 自动安装依赖
python install_requirements.py

# 或手动安装
pip install websockets websocket-client
```

### 2. 启动后端服务器
确保您的 webman 服务器已启动，并且 WebSocket 服务运行在 8791 端口。

```bash
# 在 ai_report_php 目录下启动服务器
php webman start
```

### 3. 运行测试脚本

#### 方式一：异步版本（推荐）
```bash
python test_websocket.py
```

#### 方式二：同步版本（兼容性更好）
```bash
python test_websocket_simple.py
```

## 测试流程

1. **连接建立**: 脚本连接到 WebSocket 服务器
2. **发送消息**: 发送包含学校信息的测试数据
3. **接收响应**: 监听服务器返回的流式数据
4. **解析数据**: 自动解析 MODULE 格式的返回内容
5. **显示结果**: 格式化显示分析结果

## 消息类型

### 客户端发送
```json
{
  "report_id": "报告ID",
  "school_name": "学校名称",
  "college_name": "学院名称"
}
```

### 服务器返回
- `connected`: 连接确认
- `start`: 开始处理
- `data`: 流式数据内容
- `complete`: 处理完成
- `error`: 错误信息

## 故障排除

### 连接失败
- 检查 webman 服务器是否启动
- 确认 8791 端口是否开放
- 检查防火墙设置

### 依赖问题
```bash
# 如果 websockets 安装失败
pip install websockets

# 如果 websocket-client 安装失败  
pip install websocket-client
```

### 数据库问题
- 确保数据库中存在 `school_report` 表
- 确认测试用的 `report_id=1` 记录存在
- 检查 `context` 字段是否有数据

## 测试输出示例

```
🧪 WebSocket 服务器测试工具
============================================================
正在连接到 WebSocket 服务器: ws://127.0.0.1:8791
✅ WebSocket 连接成功!
✅ 连接确认: WebSocket连接成功
🆔 连接ID: 1
📤 发送消息: {"report_id": "1", "school_name": "安徽大学", "college_name": "计算机科学与技术学院"}
🚀 开始处理: 开始分析学校信息...
📊 流式数据: [MODULE1]总成绩计算公式: 根据安徽大学2025年硕士研究生招生复试录取办法...
📋 MODULE1 - 总成绩计算公式:
   根据安徽大学2025年硕士研究生招生复试录取办法，计算机相关专业...
📈 MODULE2 - 竞争难度分析:
   安徽大学计算机专业考研竞争较为激烈，属于省内热门报考目标...
🎯 MODULE3 - 备考目标建议:
   张苏同学本科就读于皖西学院计算机专业，具备一定专业基础...
✅ 处理完成: 分析完成
🔌 WebSocket 连接已关闭
🏁 测试完成!
```

## 注意事项

1. **服务器状态**: 确保后端 WebSocket 服务器正常运行
2. **数据库连接**: 确保数据库连接正常，测试数据存在
3. **API配置**: 确认千问API配置正确（single_school 配置项）
4. **网络环境**: 确保网络连接正常，可以访问千问API
5. **超时设置**: 测试脚本设置了合理的超时时间，避免长时间等待

## 文件说明

- `test_websocket.py`: 异步版本测试脚本（功能更完整）
- `test_websocket_simple.py`: 同步版本测试脚本（兼容性更好）
- `install_requirements.py`: 依赖安装脚本
- `README_WebSocket_Test.md`: 使用说明文档
