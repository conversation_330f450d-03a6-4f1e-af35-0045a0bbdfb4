<?php

namespace app\controller;

use support\Log;
use Workerman\Connection\TcpConnection;
use Workerman\Worker;
use Workerman\Timer;

class SingleSchoolWebSocketController
{
    /**
     * WebSocket 连接建立时触发
     */
    public function onConnect(TcpConnection $connection)
    {
        Log::info('WebSocket连接建立', ['connection_id' => $connection->id]);
        
        // 发送连接成功消息
        $connection->send(json_encode([
            'type' => 'connected',
            'message' => 'WebSocket连接成功',
            'connection_id' => $connection->id
        ]));
    }

    /**
     * 接收到消息时触发
     */
    public function onMessage(TcpConnection $connection, $data)
    {
        try {
            $message = json_decode($data, true);
            
            if (!$message) {
                $connection->send(json_encode([
                    'type' => 'error',
                    'message' => '消息格式错误'
                ]));
                return;
            }

            Log::info('收到WebSocket消息', $message);

            // 验证必要参数
            if (!isset($message['report_id']) || !isset($message['school_name']) || !isset($message['college_name'])) {
                $connection->send(json_encode([
                    'type' => 'error',
                    'message' => '缺少必要参数：report_id, school_name, college_name'
                ]));
                return;
            }

            // 处理单个学校分析请求
            $this->processSingleSchoolAnalysis($connection, $message);

        } catch (\Exception $e) {
            Log::error('WebSocket消息处理错误', ['error' => $e->getMessage()]);
            $connection->send(json_encode([
                'type' => 'error',
                'message' => '消息处理失败：' . $e->getMessage()
            ]));
        }
    }

    /**
     * 连接关闭时触发
     */
    public function onClose(TcpConnection $connection)
    {
        Log::info('WebSocket连接关闭', ['connection_id' => $connection->id]);
    }

    /**
     * 处理单个学校分析
     */
    private function processSingleSchoolAnalysis(TcpConnection $connection, $message)
    {
        $reportId = $message['report_id'];
        $schoolName = $message['school_name'];
        $collegeName = $message['college_name'];

        try {
            // 从数据库获取 context
            $context = db("school_report")->where("id", $reportId)->value("context");
            
            if (!$context) {
                $connection->send(json_encode([
                    'type' => 'error',
                    'message' => '未找到报告数据'
                ]));
                return;
            }

            // 截取"目标偏好："之前的数据
            $targetPreferencePos = strpos($context, '目标偏好：');
            if ($targetPreferencePos === false) {
                $baseContext = $context;
            } else {
                $baseContext = substr($context, 0, $targetPreferencePos);
            }

            // 拼接新的学校和学院信息
            $newContext = $baseContext . "目标偏好：\n";
            $newContext .= "目标学校：{$schoolName}\n";
            $newContext .= "目标学院：{$collegeName}\n";

            Log::info('构建的新context', ['context' => $newContext]);

            // 发送开始处理消息
            $connection->send(json_encode([
                'type' => 'start',
                'message' => '开始分析学校信息...'
            ]));

            // 调用千问API进行流式处理，确保英文单词完整
            $this->streamQianwenWithWordIntegrity($newContext, function($chunk) use ($connection) {
                // 发送流式数据

                Log::info('千问API单个学校流式请求', ['data' => $chunk]);
                $connection->send(json_encode([
                    'type' => 'stream_data',
                    'content' => $chunk
                ]));
            }, function() use ($connection) {
                // 流式处理完成
                $connection->send(json_encode([
                    'type' => 'complete',
                    'message' => '分析完成'
                ]));
            });

        } catch (\Exception $e) {
            Log::error('单个学校分析处理错误', ['error' => $e->getMessage()]);
            $connection->send(json_encode([
                'type' => 'error',
                'message' => '分析处理失败：' . $e->getMessage()
            ]));
        }
    }

    /**
     * 调用千问API进行流式处理，确保英文单词完整（单个学校版本）
     */
    private function streamQianwenWithWordIntegrity($content, $dataCallback, $completeCallback)
    {
        try {
            // 获取单个学校配置
            $appId = config('qianwen.single_school')['appid'];
            $apiKey = config('qianwen.single_school')['key'];

            if (empty($appId) || empty($apiKey)) {
                throw new \Exception('千问API单个学校配置信息不完整');
            }

            $url = "https://dashscope.aliyuncs.com/api/v1/apps/$appId/completion";

            // 构建请求数据
            $data = [
                "input" => [
                    'prompt' => $content
                ],
                "parameters" => [
                    "incremental_output" => true  // 使用流式输出
                ]
            ];

            $dataString = json_encode($data);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("JSON encoding failed with error: " . json_last_error_msg());
            }

            Log::info('千问API单个学校流式请求', ['url' => $url, 'data' => $dataString]);

            // 初始化 cURL
            $ch = curl_init($url);

            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $dataString);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);  // 流式处理
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey,
                'X-DashScope-SSE: enable'
            ]);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 600);

            // 设置流式处理回调，确保英文单词完整
            $buffer = '';
            $pendingText = '';
            curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl_handle, $data) use ($dataCallback, &$buffer, &$pendingText) {
                $buffer .= $data;
                $lines = explode("\n", $buffer);
                $buffer = array_pop($lines); // 保留最后一行（可能不完整）

                foreach ($lines as $line) {
                    $line = trim($line);
                    if (empty($line)) continue;

                    if (strpos($line, 'data:') === 0) {
                        $jsonData = trim(substr($line, 5));
                        if ($jsonData === '[DONE]') {
                            // 发送剩余的待处理文本
                            if (!empty($pendingText)) {
                              
                                $dataCallback($pendingText);
                                $pendingText = '';
                            }
                            continue;
                        }

                        $decoded = json_decode($jsonData, true);
                        if ($decoded && isset($decoded['output']['text'])) {
                            $newText = $decoded['output']['text'];

                            // 将新文本添加到待处理文本中
                            $pendingText = $newText;

                            // 检查是否可以发送完整的单词
                            $readyText = $this->extractCompleteWords($pendingText);
                            if (!empty($readyText)) {
                                $dataCallback($readyText);
                                // 更新待处理文本，移除已发送的部分
                                $pendingText = substr($pendingText, strlen($readyText));
                            }
                        }
                    }
                }

                return strlen($data);
            });

            // 执行请求
            curl_exec($ch);

            if (curl_error($ch)) {
                throw new \Exception('cURL错误: ' . curl_error($ch));
            }

            curl_close($ch);

            // 调用完成回调
            $completeCallback();

        } catch (\Exception $e) {
            Log::error('千问API单个学校流式调用失败', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 提取完整的单词，确保英文单词不被截断
     */
    private function extractCompleteWords($text)
    {
        if (empty($text)) {
            return '';
        }

        // 如果文本长度小于50个字符，等待更多内容
        if (mb_strlen($text, 'UTF-8') < 50) {
            return '';
        }

        // 查找最后一个完整单词的位置
        $lastSpacePos = strrpos($text, ' ');
        $lastPunctuationPos = max(
            strrpos($text, '.'),
            strrpos($text, ','),
            strrpos($text, '!'),
            strrpos($text, '?'),
            strrpos($text, ';'),
            strrpos($text, ':'),
            strrpos($text, '。'),
            strrpos($text, '，'),
            strrpos($text, '！'),
            strrpos($text, '？')
        );

        // 取最后一个空格或标点符号的位置
        $cutPos = max($lastSpacePos, $lastPunctuationPos);

        if ($cutPos === false || $cutPos < 30) {
            // 如果没有找到合适的切分点，或者切分点太靠前，返回空字符串等待更多内容
            return '';
        }

        // 返回到切分点的完整文本
        return substr($text, 0, $cutPos + 1);
    }

    /**
     * 从文本中解析JSON数据
     */
    private function parseJsonFromText($text)
    {
        // 如果文本本身就是JSON格式
        $decoded = json_decode($text, true);
        if ($decoded !== null) {
            return $decoded;
        }

        // 如果文本包含JSON，尝试提取
        if (preg_match('/\{.*\}/s', $text, $matches)) {
            $jsonText = $matches[0];
            $decoded = json_decode($jsonText, true);
            if ($decoded !== null) {
                return $decoded;
            }
        }

        // 如果无法解析为JSON，返回原始文本结构
        return [
            'score_formula' => $this->extractContent($text, '总成绩计算公式'),
            'difficulty_analysis' => $this->extractContent($text, '竞争难度分析'),
            'study_suggestion' => $this->extractContent($text, '备考目标建议')
        ];
    }

    /**
     * 从文本中提取指定内容
     */
    private function extractContent($text, $label)
    {
        // 查找标签位置
        $pos = strpos($text, $label);
        if ($pos === false) {
            return '';
        }

        // 提取标签后的内容
        $content = substr($text, $pos + strlen($label));

        // 清理内容
        $content = trim($content, ': ');

        // 查找下一个标签的位置来确定内容边界
        $nextLabels = ['总成绩计算公式', '竞争难度分析', '备考目标建议'];
        $minPos = strlen($content);

        foreach ($nextLabels as $nextLabel) {
            if ($nextLabel !== $label) {
                $nextPos = strpos($content, $nextLabel);
                if ($nextPos !== false && $nextPos < $minPos) {
                    $minPos = $nextPos;
                }
            }
        }

        if ($minPos < strlen($content)) {
            $content = substr($content, 0, $minPos);
        }

        return trim($content);
    }
}
