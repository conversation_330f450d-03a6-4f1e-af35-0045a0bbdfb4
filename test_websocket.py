#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket 客户端测试脚本
用于测试单个学校分析的 WebSocket 服务器
"""

import asyncio
import websockets
import json
import sys

class WebSocketTester:
    def __init__(self, uri="ws://127.0.0.1:8791"):
        self.uri = uri
        self.websocket = None
        
    async def connect(self):
        """连接到 WebSocket 服务器"""
        try:
            print(f"正在连接到 WebSocket 服务器: {self.uri}")
            self.websocket = await websockets.connect(self.uri)
            print("✅ WebSocket 连接成功!")
            return True
        except Exception as e:
            print(f"❌ WebSocket 连接失败: {e}")
            return False
    
    async def send_message(self, message):
        """发送消息到服务器"""
        if not self.websocket:
            print("❌ WebSocket 未连接")
            return
            
        try:
            message_json = json.dumps(message, ensure_ascii=False)
            print(f"📤 发送消息: {message_json}")
            await self.websocket.send(message_json)
        except Exception as e:
            print(f"❌ 发送消息失败: {e}")
    
    async def listen_messages(self):
        """监听服务器消息"""
        if not self.websocket:
            print("❌ WebSocket 未连接")
            return
            
        try:
            print("👂 开始监听服务器消息...")
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError:
                    print(f"📥 收到非JSON消息: {message}")
        except websockets.exceptions.ConnectionClosed:
            print("🔌 WebSocket 连接已关闭")
        except Exception as e:
            print(f"❌ 监听消息时出错: {e}")
    
    async def handle_message(self, data):
        """处理服务器消息"""
        message_type = data.get('type', 'unknown')
        
        if message_type == 'connected':
            print(f"✅ 连接确认: {data.get('message', '')}")
            print(f"🆔 连接ID: {data.get('connection_id', '')}")
            
        elif message_type == 'start':
            print(f"🚀 开始处理: {data.get('message', '')}")
            
        elif message_type == 'data':
            content = data.get('content', '')
            print(f"📊 流式数据: {content[:100]}{'...' if len(content) > 100 else ''}")
            
            # 解析 MODULE 格式数据
            self.parse_module_data(content)
            
        elif message_type == 'complete':
            print(f"✅ 处理完成: {data.get('message', '')}")
            
        elif message_type == 'error':
            print(f"❌ 服务器错误: {data.get('message', '')}")
            
        else:
            print(f"📥 未知消息类型 '{message_type}': {data}")
    
    def parse_module_data(self, content):
        """解析 MODULE 格式的数据"""
        import re
        
        # 匹配 MODULE 格式
        module_pattern = r'\[MODULE(\d+)\](.*?)\[/MODULE\1\]'
        matches = re.findall(module_pattern, content, re.DOTALL)
        
        for module_num, module_content in matches:
            module_content = module_content.strip()
            
            if module_num == '1':
                print(f"📋 MODULE1 - 总成绩计算公式:")
                if '总成绩计算公式:' in module_content:
                    formula = module_content.replace('总成绩计算公式:', '').strip()
                    print(f"   {formula}")
                    
            elif module_num == '2':
                print(f"📈 MODULE2 - 竞争难度分析:")
                if '竞争难度分析:' in module_content:
                    analysis = module_content.replace('竞争难度分析:', '').strip()
                    print(f"   {analysis[:200]}{'...' if len(analysis) > 200 else ''}")
                    
            elif module_num == '3':
                print(f"🎯 MODULE3 - 备考目标建议:")
                if '备考目标建议:' in module_content:
                    suggestion = module_content.replace('备考目标建议:', '').strip()
                    print(f"   {suggestion[:200]}{'...' if len(suggestion) > 200 else ''}")
    
    async def close(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()
            print("🔌 WebSocket 连接已关闭")

async def test_single_school_analysis():
    """测试单个学校分析功能"""
    tester = WebSocketTester()
    
    # 连接到服务器
    if not await tester.connect():
        return
    
    # 准备测试数据
    test_message = {
        "report_id": "1",  # 测试用的报告ID
        "school_name": "安徽大学",
        "college_name": "计算机科学与技术学院"
    }
    
    try:
        # 创建监听任务
        listen_task = asyncio.create_task(tester.listen_messages())
        
        # 等待一秒让连接稳定
        await asyncio.sleep(1)
        
        # 发送测试消息
        await tester.send_message(test_message)
        
        # 等待处理完成（最多等待60秒）
        try:
            await asyncio.wait_for(listen_task, timeout=60.0)
        except asyncio.TimeoutError:
            print("⏰ 等待超时，测试结束")
            listen_task.cancel()
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断测试")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    finally:
        await tester.close()

def print_usage():
    """打印使用说明"""
    print("=" * 60)
    print("🧪 WebSocket 服务器测试工具")
    print("=" * 60)
    print("功能: 测试单个学校分析的 WebSocket 服务器")
    print("服务器地址: ws://127.0.0.1:8791")
    print("")
    print("测试数据:")
    print("  - report_id: 1")
    print("  - school_name: 安徽大学")
    print("  - college_name: 计算机科学与技术学院")
    print("")
    print("预期返回:")
    print("  - MODULE1: 总成绩计算公式")
    print("  - MODULE2: 竞争难度分析")
    print("  - MODULE3: 备考目标建议")
    print("=" * 60)
    print("")

async def main():
    """主函数"""
    print_usage()
    
    # 检查是否需要安装依赖
    try:
        import websockets
    except ImportError:
        print("❌ 缺少依赖包 websockets")
        print("请运行: pip install websockets")
        sys.exit(1)
    
    print("🚀 开始测试...")
    print("按 Ctrl+C 可以随时中断测试")
    print("")
    
    await test_single_school_analysis()
    
    print("")
    print("🏁 测试完成!")

if __name__ == "__main__":
    # 运行测试
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
