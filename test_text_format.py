#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文本格式解析功能
"""

import re

def extract_field_content(content, field_name):
    """提取字段内容"""
    # 查找字段开始位置
    field_pattern = re.compile(rf'{field_name}:\s*"([^"]*)"', re.IGNORECASE)
    match = field_pattern.search(content)
    
    if match and match.group(1):
        # 提取引号内的内容，并处理转义字符
        field_content = match.group(1)
        
        # 处理常见的转义字符
        field_content = field_content.replace('\\n', '\n')
        field_content = field_content.replace('\\"', '"')
        field_content = field_content.replace('\\\\', '\\')
        
        return field_content.strip()
    
    return ''

def test_text_format_parsing():
    """测试文本格式解析"""
    
    # 测试数据
    test_content = '''
    score_formula: "总成绩计算公式: 根据南京农业大学2024年硕士研究生复试录取办法，总成绩 = 初试成绩 + 复试成绩",
    
    competition_analysis: "竞争难度分析: 南京农业大学085400电子信息专业竞争较为激烈，尤其计算机技术方向。报录比达6:1，复试分数线为320分。",
    
    study_suggestions: "备考目标建议: 张一同学本科为计算机科学与技术专业，建议目标分数定为380分以上，提升英语至80分。"
    '''
    
    print("🧪 测试文本格式解析功能")
    print("=" * 60)
    print("测试内容:")
    print(test_content)
    print("=" * 60)
    
    # 解析三个字段
    score_formula = extract_field_content(test_content, 'score_formula')
    competition_analysis = extract_field_content(test_content, 'competition_analysis')
    study_suggestions = extract_field_content(test_content, 'study_suggestions')
    
    print("解析结果:")
    print("")
    
    if score_formula:
        print("📋 总成绩计算公式:")
        print(f"   {score_formula}")
        print("")
    else:
        print("❌ 未解析到总成绩计算公式")
        print("")
        
    if competition_analysis:
        print("📈 竞争难度分析:")
        print(f"   {competition_analysis}")
        print("")
    else:
        print("❌ 未解析到竞争难度分析")
        print("")
        
    if study_suggestions:
        print("🎯 备考目标建议:")
        print(f"   {study_suggestions}")
        print("")
    else:
        print("❌ 未解析到备考目标建议")
        print("")
    
    # 统计结果
    parsed_count = sum([bool(score_formula), bool(competition_analysis), bool(study_suggestions)])
    print("=" * 60)
    print(f"📊 解析结果: {parsed_count}/3 个字段解析成功")
    
    if parsed_count == 3:
        print("✅ 所有字段解析成功!")
    else:
        print("⚠️  部分字段解析失败")
    
    print("=" * 60)

def test_partial_content():
    """测试部分内容解析（模拟流式数据）"""
    print("\n🔄 测试部分内容解析（模拟流式数据）")
    print("=" * 60)
    
    # 模拟流式数据的不同阶段
    partial_contents = [
        'score_formula: "总成绩计算公式: 根据南京农业大学',
        'score_formula: "总成绩计算公式: 根据南京农业大学2024年硕士研究生复试录取办法"',
        'score_formula: "总成绩计算公式: 根据南京农业大学2024年硕士研究生复试录取办法", competition_analysis: "竞争难度分析: 南京农业大学085400电子信息专业',
        'score_formula: "总成绩计算公式: 根据南京农业大学2024年硕士研究生复试录取办法", competition_analysis: "竞争难度分析: 南京农业大学085400电子信息专业竞争较为激烈"'
    ]
    
    for i, content in enumerate(partial_contents, 1):
        print(f"\n--- 阶段 {i} ---")
        print(f"内容长度: {len(content)} 字符")
        
        score_formula = extract_field_content(content, 'score_formula')
        competition_analysis = extract_field_content(content, 'competition_analysis')
        study_suggestions = extract_field_content(content, 'study_suggestions')
        
        parsed_fields = []
        if score_formula:
            parsed_fields.append("score_formula")
        if competition_analysis:
            parsed_fields.append("competition_analysis")
        if study_suggestions:
            parsed_fields.append("study_suggestions")
            
        print(f"解析到的字段: {', '.join(parsed_fields) if parsed_fields else '无'}")

if __name__ == "__main__":
    test_text_format_parsing()
    test_partial_content()
