<?php
namespace app\controller;

use app\model\School;
use app\model\SchoolMajor;
use app\model\Major;
use app\model\SchoolInfo;
use app\model\NationalLine;
use app\model\AdmissionList;
use app\model\RetestList;
use app\model\SchoolBasicInfo;
use app\model\SchoolReport;
use support\Request;
use support\Log;
use support\Db;
use app\model\FirstLevelDisciplineCode;
use app\model\Student;

class SchoolController
{
    /**
     * 搜索学校
     * @param Request $request
     * @return \support\Response
     */
    public function search(Request $request)
    {
        $keyword = $request->get('keyword', '');
        if (empty($keyword)) {
            return json([
                'code' => 400,
                'msg' => '请输入搜索关键词',
                'data' => []
            ]);
        }

        $schools = School::where('name', 'like', "%{$keyword}%")
            ->field(['id', 'name'])
            ->select();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $schools
        ]);
    }

    /**
     * 搜索专业
     * @param Request $request
     * @return \support\Response
     */
    public function searchSchoolMajor(Request $request)
    {
        $schoolId = $request->get('school_id', 0);
        $majorName = $request->get('major_name', '');
        $page = (int)$request->get('page', 1); // 页码，默认第1页
        $limit = (int)$request->get('limit', 10); // 每页数量，默认10条

        // 验证学校ID
        if (empty($schoolId)) {
            return json([
                'code' => 400,
                'msg' => '请选择学校',
                'data' => []
            ]);
        }

        // 构建查询条件
        $query = SchoolMajor::where('school_id', $schoolId)
            ->where('is_delete', 0)
            ->where('type', 0); // 只查询专业，不查询门类

        // 如果有专业名称，添加模糊搜索条件
        if (!empty($majorName)) {
            $query = $query->where('major_name', 'like', "%{$majorName}%");
        }

        // 获取总数
        $total = $query->count();

        // 计算偏移量
        $offset = ($page - 1) * $limit;

        // 获取分页数据
        $majors = $query->field([
            'id',
            'major_name',
        ])->limit($offset, $limit)->select();

        // 计算分页信息
        $totalPages = ceil($total / $limit);
        $hasMore = $page < $totalPages;

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $majors,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => $totalPages,
                'has_more' => $hasMore
            ]
        ]);
    }

    /**
     * 搜索二级学科
     * @param Request $request
     * @return \support\Response
     */
    public function searchMajor(Request $request)
    {
        $keyword = $request->get('keyword', '');

        if (empty($keyword)) {
            return json([
                'code' => 400,
                'msg' => '请输入搜索关键词',
                'data' => []
            ]);
        }

        $result = Major::where('erji_name', 'like', "%{$keyword}%")
            ->field([
                'erji_name as name',
                'major_code as code'
            ])
            ->select();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result
        ]);
    }

    /**
     * 获取考研年份列表
     * @return \support\Response
     */
    public function getExamYears()
    {
        // 从配置文件中获取考研年份列表
        $years = config('exam_years.years', []);

        // 转换为前端需要的格式
        $result = [];
        foreach ($years as $key => $value) {
            $result[] = [
                'value' => $key,
                'label' => $value
            ];
        }

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result
        ]);
    }

    /**
     * 根据预估分数和专业信息获取院校数据
     * 该接口不需要鉴权
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getSchoolsByScore(Request $request)
    {
        // 记录请求参数
        $requestParams = $request->all();
        Log::info('获取院校数据请求参数: ' . json_encode($requestParams, JSON_UNESCAPED_UNICODE));

        // 获取请求参数
        $score = (int)$request->get('score', 0); // 预估分数
        $majorCode = $request->get('major_code', ''); // 专业代码
        $majorName = $request->get('major_name', ''); // 专业名称
        $provinces = $request->get('provinces', ''); // 省份，多个省份用逗号分隔
        $limit = (int)$request->get('limit', 20); // 返回数量限制，默认20条

        // 验证参数
        if ($score <= 0) {
            return json([
                'code' => 400,
                'msg' => '预估分数不能为空',
                'data' => []
            ]);
        }

        // 构建查询条件
        $query = SchoolInfo::where('must_reach_score', '<=', $score);

        // 如果有专业代码，添加专业代码条件
        if (!empty($majorCode)) {
            $query = $query->where('second_level_name', $majorCode);
        }

        // 如果有专业名称，添加专业名称条件
        if (!empty($majorName)) {
            $query = $query->where('major_name', 'like', "%{$majorName}%");
        }

        // 如果有省份，添加省份条件
        if (!empty($provinces)) {
            $provinceArray = explode(',', $provinces);
            if (!empty($provinceArray)) {
                $query = $query->whereIn('province', $provinceArray);
            }
        }

        // 按必达分排序（从高到低）
        $query = $query->order('must_reach_score', 'desc');

        // 获取数据
        $schools = $query->limit($limit)->select();

        // 处理返回数据
        $result = [];
        foreach ($schools as $school) {
            // 计算分差
            $scoreDiff = $score - $school['must_reach_score'];

            // 构建返回数据
            $result[] = [
                'id' => $school['id'],
                'school_name' => $school['school_name'],
                'province' => $school['province'],
                'area' => $school['area'],
                'college' => $school['college'],
                'major_name' => $school['major_name'],
                'first_level_code' => $school['first_level_code'],
                'xueke_name' => $school['xueke_name'],
                'second_level_name' => $school['second_level_name'],
                'school_type' => $school['school_type'],
                'study_type' => $school['study_type'],
                'warning_level' => $school['warning_level'],
                'must_reach_score' => $school['must_reach_score'],
                'score_diff' => $scoreDiff,
                'ranking' => $school['ranking'],
                'admission' => $school['admission'],
                'course_suggestion' => $school['course_suggestion'],
                'detail_url' => $school['detail_url'],
            ];
        }

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result
        ]);
    }

    /**
     * 获取国家线数据
     * 根据一级学科ID获取对应一级学科的最近三年国家线数据
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getNationalLineData(Request $request)
    {
        $firstLevelDiscipline = $request->get('firstLevelDiscipline', '');

        // 验证参数
        if (empty($firstLevelDiscipline)) {
            return json([
                'code' => 400,
                'msg' => '一级学科不能为空',
                'data' => []
            ]);
        }

        $firstLevelDisciplineInfo = FirstLevelDisciplineCode::where('id|yiji_value', $firstLevelDiscipline)->field('yiji_value,yiji_name')->find(); // 获取一级学科对应的专业代码，例如：085400
        $firstLevelDisciplineCode = $firstLevelDisciplineInfo['yiji_value'];
        Log::info('一级学科代码: ' . $firstLevelDiscipline . ', 对应的专业代码: ' . $firstLevelDisciplineCode);

        // 记录请求参数
        Log::info('获取国家线数据请求参数: ' . json_encode(['firstLevelDisciplineCode' => $firstLevelDisciplineCode], JSON_UNESCAPED_UNICODE));

        try {
            // 使用模型获取国家线数据
            $result = NationalLine::getNationalLineBySubjectCode($firstLevelDisciplineCode);
            $result['first_level_discipline'] = $firstLevelDisciplineInfo['yiji_name'];
            if (empty($result)) {
                return json([
                    'code' => 400,
                    'msg' => '未找到对应的国家线数据',
                    'data' => []
                ]);
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('获取国家线数据异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '获取国家线数据失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 获取学校信息列表（用于添加院校功能）
     * 支持搜索和分页，根据学生的目标省份和专业进行过滤
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getSchoolInfoList(Request $request)
    {
        $page = (int)$request->get('page', 1);
        $limit = (int)$request->get('limit', 20);
        $keyword = $request->get('keyword', '');
        $reportId = $request->get('report_id', '');

        // 记录请求参数
        Log::info('获取学校信息列表请求参数: ' . json_encode([
            'page' => $page,
            'limit' => $limit,
            'keyword' => $keyword,
            'report_id' => $reportId
        ], JSON_UNESCAPED_UNICODE));

        try {
            // 构建查询条件
            $query = new SchoolInfo;

            // 如果有report_id，获取学生的目标省份和专业代码作为硬性条件
            if (!empty($reportId)) {
                $report = SchoolReport::where('id', $reportId)->where('is_delete', 0)->find();
                 $student = Student::where('id', $report['student_id'])->where('is_delete', 0)->find();
                $studentInfo = $this->getStudentInfoByReportId($reportId);
                $studyType = $student['educational_style']=="0"?"全日制":"非全日制";

                if(!empty($studyType)){
                    $query = $query->where("study_type", $studyType);
                }

                if ($studentInfo) {
                    // 处理目标省份（逗号分隔）
                    if (!empty($studentInfo['target_provinces'])) {
                        $targetProvinces = explode(',', $studentInfo['target_provinces']);
                        $targetProvinces = array_map('trim', $targetProvinces);
                         $query = $query->whereIn('province', $targetProvinces);
                    }

                    // 处理目标专业代码（逗号分隔）
                    if (!empty($studentInfo['target_major_code'])) {
                        $targetMajorCodes = explode(',', $studentInfo['target_major_code']);
                        $targetMajorCodes = array_map('trim', $targetMajorCodes);
                        $query =$query->whereIn('major_code', $targetMajorCodes);
                    }
                    if (!empty($studentInfo['school_level'])) {
                        $targetSchoolLevel = explode(',', $studentInfo['school_level']);
                        $targetSchoolLevel = array_map('trim', $targetSchoolLevel);

                        $query->where(function($q) use ($targetSchoolLevel) {
                            foreach ($targetSchoolLevel as $value) {
                                if($value == "985"){
                                    $q->whereOr("school_type","985,211,双一流");
                                }
                                if($value == "211"){
                                    $q->whereOr("school_type","211,双一流");
                                }
                                if($value == "双一流"){
                                    $q->whereOr("school_type","双一流");
                                }
                                if($value == "双非"){
                                    $q->whereOr("school_type","");
                                }
                            }
                        });
                    }
                }
            }

            // 如果有搜索关键词，添加搜索条件
            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->where('school_name', 'like', "%{$keyword}%")
                      ->whereOr('major_name', 'like', "%{$keyword}%")
                      ->whereOr('province', 'like', "%{$keyword}%")
                      ->whereOr('area', 'like', "%{$keyword}%");
                });
            }

            // 获取总数
            $total = $query->count();

            // 获取分页数据，排除指定字段
            $schools = $query->field([
                'id', 'province', 'xueke_name', 'first_level_code', 'second_level_name',
                'school_name', 'major_code', 'major_name', 'area', 'school_type',
                'college', 'study_type', 'warning_level', 'ranking', 'admission',
                'course_suggestion', 'must_reach_score', 'province_city'
            ])
            ->order('id', 'desc')
            ->limit($limit)
            ->page($page)
            ->select();
            echo db('school_info')->getLastSql();    
            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'list' => $schools,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取学校信息列表异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '获取学校信息列表失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 根据报告ID获取学生基本信息
     *
     * @param string $reportId
     * @return array|null
     */
    private function getStudentInfoByReportId($reportId)
    {
        try {
            // 通过报告ID获取学生信息
            $studentInfo = SchoolReport::where('id', $reportId)
                ->field(['target_provinces', 'target_major_code',"school_level"])
                ->find();
            return $studentInfo ? $studentInfo->toArray() : null;
        } catch (\Exception $e) {
            Log::error('获取学生基本信息异常: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取院校详细信息
     * 包括拟录取名单、复试名单和基本信息
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getSchoolDetailInfo(Request $request)
    {
        $schoolId = $request->get('school_id', '');
        $year = $request->get('year', '2025');

        // 验证参数
        if (empty($schoolId)) {
            return json([
                'code' => 400,
                'msg' => '学校ID不能为空',
                'data' => []
            ]);
        }

        // 记录请求参数
        Log::info('获取院校详细信息请求参数: ' . json_encode([
            'school_id' => $schoolId,
            'year' => $year
        ], JSON_UNESCAPED_UNICODE));

        try {
            // 首先检查 ba_school_info 表的 detail_url_status 字段
            $schoolInfo = SchoolInfo::where('id', $schoolId)
                ->field(['detail_url_status', 'school_name'])
                ->find();

            if (!$schoolInfo) {
                return json([
                    'code' => 404,
                    'msg' => '未找到对应的学校信息',
                    'data' => []
                ]);
            }

            $detailUrlStatus = $schoolInfo->detail_url_status;

            // 根据 detail_url_status 字段判断处理逻辑
            if ($detailUrlStatus == 0) {
                // 状态为0，调用爬虫并返回提示信息
                Log::info("学校ID {$schoolId} 数据状态为0，开始调用爬虫");  
                $remoteController = new RemoteController();
                $remoteController->callSpiderApi([$schoolId]);
                return json([
                    'code' => 202,
                    'msg' => '当前数据爬取中，稍后再试',
                    'data' => []
                ]);
            } elseif ($detailUrlStatus == 2) {
                // 状态为2，直接返回提示信息
                Log::info("学校ID {$schoolId} 数据状态为2，爬取中");
                return json([
                    'code' => 202,
                    'msg' => '当前数据爬取中，稍后再试',
                    'data' => []
                ]);
            } elseif ($detailUrlStatus != 1) {
                // 状态不为1，返回未知状态提示
                Log::warning("学校ID {$schoolId} 数据状态异常: {$detailUrlStatus}");
                return json([
                    'code' => 400,
                    'msg' => '数据状态异常，请联系管理员',
                    'data' => []
                ]);
            }

            // 状态为1，继续原有逻辑
            Log::info("学校ID {$schoolId} 数据状态为1，开始获取详细信息");
            $result = [];

            // 获取拟录取名单
            $admissionList = AdmissionList::where('school_id', $schoolId)
                ->where('year', $year)
                ->field(['id', 'name', 'college', 'major_code', 'major_name', 'initial_score', 'retest_score', 'total_score', 'first_choice_school', 'student_remark'])
                ->order('total_score', 'desc')
                ->select();

            // 获取复试名单
            $retestList = RetestList::where('school_id', $schoolId)
                ->where('year', $year)
                ->field(['id', 'name', 'college', 'major_code', 'major_name', 'politics_score', 'english_score', 'major1_score', 'major2_score', 'initial_score', 'volunteer_type', 'admission_status'])
                ->order('initial_score', 'desc')
                ->select();

            // 获取学校基本信息
            $schoolBasicInfo = SchoolBasicInfo::where('school_id', $schoolId)
                ->field(['id', 'research_direction', 'exam_range', 'reference_books', 'retest_content', 'tuition_fee', 'study_years', 'accommodation', 'admission_requirements'])
                ->find();

            // 获取完整的学校信息（前面已经获取了部分信息，这里获取完整信息）
            $schoolInfoComplete = SchoolInfo::where('id', $schoolId)
                ->field(['id', 'school_name', 'province', 'area', 'school_type', 'college', 'major_name', 'study_type', 'warning_level', 'must_reach_score', 'ranking', 'admission'])
                ->find();

            $result = [
                'admission_list' => $admissionList ? $admissionList->toArray() : [],
                'retest_list' => $retestList ? $retestList->toArray() : [],
                'school_basic_info' => $schoolBasicInfo ? $schoolBasicInfo->toArray() : [],
                'school_info' => $schoolInfoComplete ? $schoolInfoComplete->toArray() : []
            ];

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('获取院校详细信息异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '获取院校详细信息失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
}